import { Request, Response, NextFunction } from "express";
import PackageService from "../services/Packageservice";
import { PackageDocument } from "../models/Package";
import { asyncHandler } from "../utils/asyncHandler";
import { logInfo, logWarning } from "../utils/logger";
import { CreatePackageDto, UpdatePackageDto, QueryPackageDto } from "../types/packageTypes";

export class PackageController {
  static add = asyncHandler(async (req: Request, res: Response, _next: NextFunction) => {
    logInfo(`Creating new package`, {
      packageData: req.body,
      userId: (req as any).user?.id
    }, req);

    const dto: CreatePackageDto = req.body;
    const pkg: PackageDocument = await PackageService.create(dto);

    logInfo(`Package created successfully`, {
      packageId: pkg._id,
      packageTitle: pkg.title
    }, req);

    res.status(201).json({
      success: true,
      message: "Package created successfully",
      data: pkg,
    });
  });

  static update = asyncHandler(async (req: Request, res: Response, _next: NextFunction) => {
    const packageId = req.params.id;

    logInfo(`Updating package`, {
      packageId,
      updateData: req.body,
      userId: (req as any).user?.id
    }, req);

    const dto: UpdatePackageDto = req.body;
    const pkg = await PackageService.updateOne(packageId, dto);

    logInfo(`Package updated successfully`, {
      packageId: pkg._id,
      packageTitle: pkg.title
    }, req);

    res.status(200).json({
      success: true,
      message: "Package updated successfully",
      data: pkg,
    });
  });

  static getAll = asyncHandler(async (req: Request, res: Response, _next: NextFunction) => {
    logInfo(`Retrieving all packages`, {
      userId: (req as any).user?.id,
      query: req.query
    }, req);

    const query: QueryPackageDto = {
      page: req.query.page ? parseInt(req.query.page as string) : 1,
      limit: req.query.limit ? parseInt(req.query.limit as string) : 10,
      search: req.query.search as string,
      category: req.query.category as string,
      sortBy: req.query.sortBy as "createdAt" | "title" | "price",
      sortOrder: req.query.sortOrder as "asc" | "desc",
    };

    const { packages, total } = await PackageService.fetchAll(query);

    const pagination = {
      page: query.page!,
      limit: query.limit!,
      total,
      pages: Math.ceil(total / query.limit!),
    };

    logInfo(`Retrieved ${packages.length} packages`, {
      count: packages.length,
      total,
      pagination
    }, req);

    res.status(200).json({
      success: true,
      message: "Packages retrieved successfully",
      data: packages,
      count: packages.length,
      pagination,
    });
  });

  static getById = asyncHandler(async (req: Request, res: Response, _next: NextFunction) => {
    const packageId = req.params.id;

    logInfo(`Retrieving package by ID`, { packageId }, req);

    const pkg = await PackageService.fetchById(packageId);

    logInfo(`Package retrieved successfully`, {
      packageId: pkg._id,
      packageTitle: pkg.title
    }, req);

    res.status(200).json({
      success: true,
      message: "Package retrieved successfully",
      data: pkg,
    });
  });

  static delete = asyncHandler(async (req: Request, res: Response, _next: NextFunction) => {
    const packageId = req.params.id;

    logInfo(`Deleting package`, {
      packageId,
      userId: (req as any).user?.id
    }, req);

    const pkg = await PackageService.deleteOne(packageId);

    logInfo(`Package deleted successfully`, {
      packageId: pkg._id,
      packageTitle: pkg.title
    }, req);

    res.status(200).json({
      success: true,
      message: "Package deleted successfully",
      data: { deletedPackage: pkg },
    });
  });

  static addImages = asyncHandler(async (req: Request, res: Response, _next: NextFunction) => {
    const packageId = req.params.id;
    const { images } = req.body;

    logInfo(`Adding images to package`, {
      packageId,
      imagesCount: images?.length,
      userId: (req as any).user?.id
    }, req);

    const result = await PackageService.addImages(packageId, images);

    logInfo(`Images added successfully`, {
      packageId,
      totalImages: result.data.length
    }, req);

    res.status(200).json({
      success: true,
      message: "Images added successfully",
      data: result.data,
    });
  });
}