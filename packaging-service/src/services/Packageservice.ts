import PackageModel, { PackageDocument } from "../models/Package";
import { CreatePackageDto, UpdatePackageDto, QueryPackageDto } from "../types/packageTypes";

class PackageService {
  static async create(dto: CreatePackageDto): Promise<PackageDocument> {
    try {
      if (!dto.title || !dto.shortDescription || !dto.description) {
        throw new Error("Title, short description, and description are required");
      }

      if (!dto.variations || dto.variations.length === 0) {
        throw new Error("At least one variation is required");
      }

      if (!dto.images || dto.images.length === 0) {
        throw new Error("At least one image is required");
      }

      if (!dto.quantity || dto.quantity.length === 0) {
        throw new Error("At least one quantity option is required");
      }

      const packageData = await PackageModel.create({
        title: dto.title,
        shortDescription: dto.shortDescription,
        description: dto.description,
        categories: dto.categories,
        variations: dto.variations,
        images: dto.images,
        quantity: dto.quantity,
      });

      return packageData;
    } catch (error: any) {
      throw new Error(error.message || "Error creating package");
    }
  }

  static async fetchAll(query?: QueryPackageDto): Promise<{ packages: PackageDocument[]; total: number }> {
    try {
      const page = query?.page || 1;
      const limit = query?.limit || 10;
      const skip = (page - 1) * limit;

      let filter: any = {};
      let sort: any = { createdAt: -1 };

      // Search functionality
      if (query?.search) {
        filter.$or = [
          { title: { $regex: query.search, $options: "i" } },
          { shortDescription: { $regex: query.search, $options: "i" } },
          { description: { $regex: query.search, $options: "i" } },
        ];
      }

      // Category filter
      if (query?.category) {
        filter["categories.customPrinting"] = query.category;
      }

      // Sorting
      if (query?.sortBy) {
        const sortOrder = query.sortOrder === "desc" ? -1 : 1;
        sort = { [query.sortBy]: sortOrder };
      }

      const packages = await PackageModel.find(filter)
        .sort(sort)
        .skip(skip)
        .limit(limit)
        .lean();

      const total = await PackageModel.countDocuments(filter);

      return { packages, total };
    } catch (error: any) {
      throw new Error(error.message || "Error fetching packages");
    }
  }

  static async fetchById(id: string): Promise<PackageDocument> {
    try {
      const packageData = await PackageModel.findById(id);
      if (!packageData) {
        throw new Error("Package not found");
      }
      return packageData;
    } catch (error: any) {
      throw new Error(error.message || "Error fetching package");
    }
  }

  static async updateOne(id: string, dto: UpdatePackageDto): Promise<PackageDocument> {
    try {
      const packageData = await PackageModel.findById(id);
      if (!packageData) {
        throw new Error("Package not found");
      }

      // Update fields if provided
      if (dto.title) {
        packageData.title = dto.title;
      }
      if (dto.shortDescription) {
        packageData.shortDescription = dto.shortDescription;
      }
      if (dto.description) {
        packageData.description = dto.description;
      }
      if (dto.categories) {
        packageData.categories = { ...packageData.categories, ...dto.categories };
      }
      if (dto.variations) {
        packageData.variations = dto.variations as any;
      }
      if (dto.images) {
        packageData.images = dto.images;
      }
      if (dto.quantity) {
        packageData.quantity = dto.quantity as any;
      }

      const updatedPackage = await packageData.save();
      return updatedPackage;
    } catch (error: any) {
      throw new Error(error.message || "Error updating package");
    }
  }

  static async deleteOne(id: string): Promise<PackageDocument> {
    try {
      const packageData = await PackageModel.findByIdAndDelete(id);
      if (!packageData) {
        throw new Error("Package not found");
      }
      return packageData;
    } catch (error: any) {
      throw new Error(error.message || "Error deleting package");
    }
  }

  static async addImages(id: string, images: string[]): Promise<{ data: string[] }> {
    try {
      if (!Array.isArray(images) || images.length === 0) {
        throw new Error("Images array is required and cannot be empty");
      }

      for (const image of images) {
        if (!image || typeof image !== "string") {
          throw new Error("Each image must be a valid URL string");
        }
      }

      const packageData = await PackageModel.findById(id);
      if (!packageData) {
        throw new Error("Package not found");
      }

      packageData.images.push(...images);
      await packageData.save();

      return { data: packageData.images };
    } catch (error: any) {
      throw new Error(error.message || "Error adding images");
    }
  }
}

export default PackageService;