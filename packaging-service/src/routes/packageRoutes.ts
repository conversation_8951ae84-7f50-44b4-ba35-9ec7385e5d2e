import { Router } from "express";
import { PackageController } from "../controllers/packageController";
import { protect, adminProtect } from "../middlewares/auth";
import { validate } from "../middlewares/validate";
import { createPackageSchema, updatePackageSchema, packageIdSchema, addImagesSchema } from "../schemas/packageSchema";

const router = Router();

/**
 * @swagger
 * components:
 *   schemas:
 *     Variation:
 *       type: object
 *       required:
 *         - title
 *         - optionType
 *         - options
 *       properties:
 *         title:
 *           type: string
 *           description: The variation title
 *         optionType:
 *           type: string
 *           enum: [dropdown, checkbox, radio]
 *           description: The type of option selection
 *         options:
 *           type: array
 *           items:
 *             type: string
 *           description: Available options for this variation
 *
 *     Quantity:
 *       type: object
 *       required:
 *         - size
 *         - price
 *       properties:
 *         size:
 *           type: string
 *           description: The size or tier name
 *         price:
 *           type: number
 *           description: The price for this size/tier
 *
 *     Package:
 *       type: object
 *       required:
 *         - title
 *         - shortDescription
 *         - description
 *         - variations
 *         - images
 *         - quantity
 *       properties:
 *         _id:
 *           type: string
 *           description: The auto-generated id of the package
 *         title:
 *           type: string
 *           description: The package title
 *         shortDescription:
 *           type: string
 *           description: Brief description of the package
 *         description:
 *           type: string
 *           description: Detailed description of the package
 *         variations:
 *           type: array
 *           items:
 *             $ref: '#/components/schemas/Variation'
 *           description: Package variations/options
 *         images:
 *           type: array
 *           items:
 *             type: string
 *           description: Array of image URLs
 *         quantity:
 *           type: array
 *           items:
 *             $ref: '#/components/schemas/Quantity'
 *           description: Available quantity/pricing options
 *         createdAt:
 *           type: string
 *           format: date-time
 *           description: Package creation timestamp
 *         updatedAt:
 *           type: string
 *           format: date-time
 *           description: Package last update timestamp
 */

/**
 * @swagger
 * /api/v1/packages:
 *   post:
 *     summary: Create a new package
 *     tags: [Packages]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/Package'
 *     responses:
 *       201:
 *         description: Package created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                 data:
 *                   $ref: '#/components/schemas/Package'
 *       400:
 *         description: Validation error
 *       401:
 *         description: Unauthorized
 */
router.post("/", /* protect, adminProtect, */ validate({ body: createPackageSchema }), PackageController.add);

/**
 * @swagger
 * /api/v1/packages:
 *   get:
 *     summary: Get all packages
 *     tags: [Packages]
 *     responses:
 *       200:
 *         description: Packages retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/Package'
 */
router.get("/", PackageController.getAll);

/**
 * @swagger
 * /api/v1/packages/{id}:
 *   get:
 *     summary: Get package by ID
 *     tags: [Packages]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Package ID
 *     responses:
 *       200:
 *         description: Package retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                 data:
 *                   $ref: '#/components/schemas/Package'
 *       404:
 *         description: Package not found
 */
router.get("/:id", validate({ params: packageIdSchema }), PackageController.getById);

/**
 * @swagger
 * /api/v1/packages/{id}:
 *   patch:
 *     summary: Update package by ID
 *     tags: [Packages]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Package ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/Package'
 *     responses:
 *       200:
 *         description: Package updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                 data:
 *                   $ref: '#/components/schemas/Package'
 *       404:
 *         description: Package not found
 *       401:
 *         description: Unauthorized
 */
router.patch("/:id", protect, validate({ params: packageIdSchema, body: updatePackageSchema }), PackageController.update);

/**
 * @swagger
 * /api/v1/packages/{id}:
 *   delete:
 *     summary: Delete package by ID
 *     tags: [Packages]
 *     security:
 *       - adminAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Package ID
 *     responses:
 *       200:
 *         description: Package deleted successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *       404:
 *         description: Package not found
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Admin access required
 */
router.delete("/:id", adminProtect, validate({ params: packageIdSchema }), PackageController.delete);

/**
 * @swagger
 * /api/v1/packages/{id}/images:
 *   post:
 *     summary: Add images to package
 *     tags: [Packages]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Package ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               images:
 *                 type: array
 *                 items:
 *                   type: string
 *                 description: Array of image URLs
 *     responses:
 *       200:
 *         description: Images added successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                 data:
 *                   type: array
 *                   items:
 *                     type: string
 *       404:
 *         description: Package not found
 *       401:
 *         description: Unauthorized
 */
router.post("/:id/images", protect, validate({ params: packageIdSchema, body: addImagesSchema }), PackageController.addImages);

export default router;