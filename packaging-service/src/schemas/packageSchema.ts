import { z } from "zod";

// Removed unused imageObjectSchema since images are now string arrays

// 1. VariationOptionImage schema (for variation option images)
const VariationOptionImageSchema = z.object({
  id: z.string(),
  file: z.any().nullable(), // Use z.any() instead of z.instanceof(File) for server compatibility
  preview: z.string().nullable(),
});

// 2. VariationOption schema
const VariationOptionSchema = z.object({
  text: z.string().optional(),
  url: z.string().optional(),
  image: VariationOptionImageSchema.optional(),
});

// 3. Variations schema
const VariationsSchema = z.object({
  title: z.string(),
  optionType: z.enum(["images", "default", "dropdown"]),
  options: z.array(VariationOptionSchema),
});

// Keep backward compatibility with old variation schema
const variationSchema = z.union([
  VariationsSchema, // New schema
  z.object({        // Old schema for backward compatibility
    title: z.string().min(1, "Variation title is required"),
    optionType: z.enum(["dropdown", "checkbox", "radio", "images"]),
    options: z.array(z.object({
      text: z.string().optional().default(""),
      image: z.object({
        id: z.string().min(1, "Image ID is required"),
        fileName: z.string().min(1, "File name is required"),
        hasFile: z.boolean(),
      }).optional(),
    })).min(1, "At least one option is required"),
  })
]);

// Categories schema
const categoriesSchema = z.object({
  customPrinting: z.array(z.string()).optional(),
  packagingProducts: z.array(z.string()).optional(),
  bundlesSamples: z.array(z.string()).optional(),
  shopByCollection: z.array(z.string()).optional(),
}).optional();

// Quantity schema (unchanged)
const quantitySchema = z.object({
  size: z.string().min(1, "Size is required"),
  price: z.number().positive("Price must be positive"),
});

// Updated create package schema
export const createPackageSchema = z.object({
  title: z.string().min(1, "Title is required").max(200, "Title too long"),
  shortDescription: z.string().min(1, "Short description is required").max(1000, "Short description too long"),
  description: z.string().min(1, "Description is required"),
  categories: categoriesSchema,
  variations: z.array(variationSchema).min(1, "At least one variation is required"),
  images: z.array(z.string()).min(1, "At least one image is required"), // Changed to array of strings
  quantity: z.array(quantitySchema).min(1, "At least one quantity option is required"),
});

export const updatePackageSchema = createPackageSchema.partial();

export const packageIdSchema = z.object({
  id: z.string().regex(/^[0-9a-fA-F]{24}$/, "Invalid package ID format"),
});

// Query schema for filtering and pagination
export const queryPackageSchema = z.object({
  page: z.string().transform(Number).optional(),
  limit: z.string().transform(Number).optional(),
  search: z.string().optional(),
  category: z.string().optional(),
  sortBy: z.enum(["createdAt", "title", "price"]).optional(),
  sortOrder: z.enum(["asc", "desc"]).optional(),
});

// Add images schema
export const addImagesSchema = z.object({
  images: z.array(z.string().url("Each image must be a valid URL")).min(1, "At least one image is required"),
});
