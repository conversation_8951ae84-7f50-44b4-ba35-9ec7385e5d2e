import { Express } from "express";
import { errorHandler } from "../middlewares/errorHandler";
import { logError } from "../utils/logger";

export const setupErrorHandling = (app: Express): void => {
  // Error handler middleware (must be last)
  app.use(errorHandler);

  // Handle 404 errors
  app.use("*", (req, res) => {
    res.status(404).json({
      success: false,
      message: `Route ${req.method} ${req.originalUrl} not found`,
    });
  });

  // Global error handlers
  process.on('uncaughtException', (error) => {
    logError(error, { context: 'Uncaught Exception' });
    process.exit(1);
  });

  process.on('unhandledRejection', (reason) => {
    logError(new Error(`Unhandled Rejection: ${reason}`), {
      context: 'Unhandled Promise Rejection'
    });
    process.exit(1);
  });
};
