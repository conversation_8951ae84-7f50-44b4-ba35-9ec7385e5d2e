import { Express } from "express";
import packageRoutes from "../routes/packageRoutes";
import { setupSwagger } from "./swaggerConfig";

export const setupRoutes = (app: Express): void => {
  // Health check endpoint
  app.get("/", (_req, res) => {
    res.send("Coconut Packaging API");
  });

  // API routes
  app.use("/api/v1/packages", packageRoutes);

  // Setup Swagger API Documentation
  setupSwagger(app);
};
