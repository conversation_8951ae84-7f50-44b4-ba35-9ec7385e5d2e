import mongoose, { Document, Schema } from "mongoose";

// Removed ImageObject interface since images are now string arrays

// New VariationOptionImage interface
export interface VariationOptionImage {
  id: string;
  file: any | null; // File object or null
  preview: string | null;
}

// Updated VariationOption interface
export interface VariationOption {
  text?: string;
  url?: string;
  image?: VariationOptionImage;
}

// Updated Variation interface
export interface Variation {
  title: string;
  optionType: "images" | "default" | "dropdown" | "checkbox" | "radio"; // Added new types
  options: VariationOption[];
}

// Categories interface
export interface Categories {
  customPrinting?: string[];
  packagingProducts?: string[];
  bundlesSamples?: string[];
  shopByCollection?: string[];
}

// Quantity interface (unchanged)
export interface Quantity {
  size: string;
  price: number;
}

// Updated Package Document interface
export interface PackageDocument extends Document {
  title: string;
  shortDescription: string;
  description: string;
  categories?: Categories;
  variations: Variation[];
  images: string[]; // Changed to array of strings
  quantity: Quantity[];
  createdAt: Date;
  updatedAt: Date;
}

// Removed ImageObjectSchema since images are now string arrays

// New VariationOptionImage schema
const VariationOptionImageSchema = new Schema({
  id: {
    type: String,
    required: true,
  },
  file: {
    type: Schema.Types.Mixed, // Can be any type (File object, null, etc.)
    default: null,
  },
  preview: {
    type: String,
    default: null,
  },
}, { _id: false });

// Updated Variation option schema
const VariationOptionSchema = new Schema({
  text: {
    type: String,
  },
  url: {
    type: String,
  },
  image: VariationOptionImageSchema,
}, { _id: false });

// Updated Variation schema
const VariationSchema = new Schema<Variation>({
  title: {
    type: String,
    required: true,
  },
  optionType: {
    type: String,
    enum: ["images", "default", "dropdown", "checkbox", "radio"],
    required: true,
  },
  options: [VariationOptionSchema],
});

// Categories schema
const CategoriesSchema = new Schema({
  customPrinting: [{
    type: String,
  }],
  packagingProducts: [{
    type: String,
  }],
  bundlesSamples: [{
    type: String,
  }],
  shopByCollection: [{
    type: String,
  }],
}, { _id: false });

// Quantity schema (unchanged)
const QuantitySchema = new Schema<Quantity>({
  size: {
    type: String,
    required: true,
  },
  price: {
    type: Number,
    required: true,
  },
});

// Updated Package schema
const PackageSchema = new Schema<PackageDocument>({
  title: {
    type: String,
    required: true,
  },
  shortDescription: {
    type: String,
    required: true,
  },
  description: {
    type: String,
    required: true,
  },
  categories: CategoriesSchema,
  variations: [VariationSchema],
  images: [{
    type: String, // Array of strings
    required: true,
  }],
  quantity: [QuantitySchema],
}, {
  timestamps: true,
});

export default mongoose.model<PackageDocument>("Package", PackageSchema);