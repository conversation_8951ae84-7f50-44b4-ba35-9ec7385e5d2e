import winston from "winston";
import DailyRotateFile from "winston-daily-rotate-file";
import { env } from "./envValidator";
import path from "path";
import { Request } from "express";
import { createPackagingLogger } from "./winstonFactory";

// Create the main Winston error handler instance
const winstonErrorHandler = createPackagingLogger();

// Custom format for deeply nested error logging
const errorStackFormat = winston.format((info) => {
  if (info.error && info.error instanceof Error) {
    info.stack = info.error.stack;
    info.errorName = info.error.name;
    info.errorMessage = info.error.message;
    
    // Handle nested errors and circular references
    info.errorDetails = JSON.stringify(info.error, Object.getOwnPropertyNames(info.error), 2);
    
    // Extract additional error properties
    const errorProps: any = {};
    Object.getOwnPropertyNames(info.error).forEach(key => {
      if (key !== 'name' && key !== 'message' && key !== 'stack') {
        try {
          errorProps[key] = (info.error as any)[key];
        } catch (e) {
          errorProps[key] = '[Circular Reference or Non-serializable]';
        }
      }
    });
    
    if (Object.keys(errorProps).length > 0) {
      info.errorProperties = errorProps;
    }
  }
  return info;
});

// Custom format for request context with full IP extraction
const requestContextFormat = winston.format((info) => {
  if (info.req) {
    const req = info.req as Request;

    // Extract full IP address (handle IPv6 mapped IPv4)
    let fullIp = req.ip || req.socket?.remoteAddress;
    if (fullIp && fullIp.startsWith('::ffff:')) {
      fullIp = fullIp.substring(7); // Remove IPv6 mapping prefix
    }

    info.requestContext = {
      method: req.method,
      url: req.url,
      originalUrl: req.originalUrl,
      protocol: req.protocol,
      host: req.get('host'),
      ip: fullIp,
      userAgent: req.get('User-Agent'),
      headers: req.headers,
      body: req.method !== 'GET' ? req.body : undefined,
      params: req.params,
      query: req.query,
      timestamp: new Date().toISOString(),
    };
    delete info.req; // Remove the original req object to avoid circular references
  }
  return info;
});

// Create logs directory if it doesn't exist
const logsDir = path.join(process.cwd(), 'logs');

// Define log formats
const logFormat = winston.format.combine(
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
  errorStackFormat(),
  requestContextFormat(),
  winston.format.errors({ stack: true }),
  winston.format.json()
);

// Clean, readable formatter for plain text logs focused on errors
const readableFormatter = winston.format.printf(({ timestamp, level, message, ...meta }) => {
  let logOutput = `\n[${timestamp}] ${level.toUpperCase()}: ${message}\n`;

  // Error details - main focus
  if (meta.error || meta.errorName || meta.stack) {
    logOutput += `┌─ ERROR DETAILS\n`;
    logOutput += `│  Name: ${(meta as any).errorName || (meta as any).error?.name || 'Unknown'}\n`;
    logOutput += `│  Message: ${(meta as any).errorMessage || (meta as any).error?.message || message}\n`;

    if ((meta as any).stack || (meta as any).error?.stack) {
      logOutput += `│\n│  STACK TRACE:\n`;
      const stack = (meta as any).stack || (meta as any).error?.stack || '';
      const stackLines = stack.split('\n');
      stackLines.forEach((line: string, index: number) => {
        if (line.trim()) {
          if (index === 0) {
            logOutput += `│    → ${line}\n`;
          } else {
            logOutput += `│      ${line}\n`;
          }
        }
      });
    }

    // Additional error properties
    if ((meta as any).errorProperties && Object.keys((meta as any).errorProperties).length > 0) {
      logOutput += `│\n│  ERROR PROPERTIES:\n`;
      Object.entries((meta as any).errorProperties).forEach(([key, value]) => {
        logOutput += `│    ${key}: ${JSON.stringify(value)}\n`;
      });
    }
    logOutput += `└─\n`;
  }

  // Request information (concise format)
  if ((meta as any).request || (meta as any).requestContext) {
    const req = (meta as any).request || (meta as any).requestContext;
    logOutput += `┌─ REQUEST INFO\n`;
    logOutput += `│  ${req.method || 'Unknown'} ${req.url || req.originalUrl || 'Unknown'}\n`;
    logOutput += `│  IP: ${req.ip || req.socket?.remoteAddress || 'Unknown'}\n`;
    logOutput += `│  User-Agent: ${req.userAgent || req.headers?.['user-agent'] || 'Unknown'}\n`;

    if (req.body && Object.keys(req.body).length > 0) {
      logOutput += `│  Body: ${JSON.stringify(req.body)}\n`;
    }

    if (req.params && Object.keys(req.params).length > 0) {
      logOutput += `│  Params: ${JSON.stringify(req.params)}\n`;
    }

    if (req.query && Object.keys(req.query).length > 0) {
      logOutput += `│  Query: ${JSON.stringify(req.query)}\n`;
    }
    logOutput += `└─\n`;
  }

  // Response information (if available)
  if ((meta as any).response) {
    logOutput += `┌─ RESPONSE INFO\n`;
    logOutput += `│  Status: ${(meta as any).response.statusCode}\n`;
    logOutput += `│  Response Time: ${(meta as any).response.responseTime}ms\n`;

    if ((meta as any).response.responseTime > 1000) {
      logOutput += `│  SLOW REQUEST DETECTED\n`;
    }
    logOutput += `└─\n`;
  }

  // Context information (following project pattern)
  if ((meta as any).context) {
    logOutput += `┌─ CONTEXT\n`;
    if (typeof (meta as any).context === 'object') {
      Object.entries((meta as any).context).forEach(([key, value]) => {
        logOutput += `│  ${key}: ${JSON.stringify(value)}\n`;
      });
    } else {
      logOutput += `│  ${(meta as any).context}\n`;
    }
    logOutput += `└─\n`;
  }

  return logOutput;
});

const consoleFormat = winston.format.combine(
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
  winston.format.colorize(),
  winston.format.printf(({ timestamp, level, message, stack, errorName, requestContext }) => {
    let logMessage = `${timestamp} [${level}]: ${message}`;

    if (errorName) {
      logMessage += `\n  Error: ${errorName}`;
    }

    if (requestContext && typeof requestContext === 'object') {
      const ctx = requestContext as any;
      if (ctx.method && (ctx.originalUrl || ctx.url)) {
        logMessage += `\n  Request: ${ctx.method} ${ctx.originalUrl || ctx.url}`;
      }
      if (ctx.ip) {
        logMessage += `\n  IP: ${ctx.ip}`;
      }
    }

    if (stack) {
      logMessage += `\n  Stack: ${stack}`;
    }

    return logMessage;
  })
);

// Readable format for file logs focused on errors
const readableFormat = winston.format.combine(
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
  errorStackFormat(),
  requestContextFormat(),
  winston.format.errors({ stack: true }),
  readableFormatter
);

// Configure transports
const transports: winston.transport[] = [];

// Console transport for development
if (env.NODE_ENV === 'development') {
  transports.push(
    new winston.transports.Console({
      format: consoleFormat,
      level: env.LOG_LEVEL,
    })
  );
} else {
  // Simplified console for production
  transports.push(
    new winston.transports.Console({
      format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.json()
      ),
      level: 'error',
    })
  );
}

// File transports with rotation - Clean readable format focused on errors
transports.push(
  // Error logs (readable format - main focus)
  new DailyRotateFile({
    filename: path.join(logsDir, 'error-%DATE%.log'),
    datePattern: 'YYYY-MM-DD',
    level: 'error',
    format: readableFormat,
    maxSize: env.LOG_FILE_MAX_SIZE,
    maxFiles: env.LOG_FILE_MAX_FILES,
    zippedArchive: true,
  }),

  // Combined logs (readable format)
  new DailyRotateFile({
    filename: path.join(logsDir, 'combined-%DATE%.log'),
    datePattern: 'YYYY-MM-DD',
    format: readableFormat,
    maxSize: env.LOG_FILE_MAX_SIZE,
    maxFiles: env.LOG_FILE_MAX_FILES,
    zippedArchive: true,
    level: env.LOG_LEVEL,
  }),

  // HTTP access logs (readable format)
  new DailyRotateFile({
    filename: path.join(logsDir, 'access-%DATE%.log'),
    datePattern: 'YYYY-MM-DD',
    level: 'http',
    format: readableFormat,
    maxSize: env.LOG_FILE_MAX_SIZE,
    maxFiles: env.LOG_FILE_MAX_FILES,
    zippedArchive: true,
  })
);

// Create the logger
const logger = winston.createLogger({
  level: env.LOG_LEVEL,
  format: logFormat,
  transports,
  exitOnError: false,
  // Handle uncaught exceptions and rejections
  exceptionHandlers: [
    new DailyRotateFile({
      filename: path.join(logsDir, 'exceptions-%DATE%.log'),
      datePattern: 'YYYY-MM-DD',
      format: logFormat,
      maxSize: env.LOG_FILE_MAX_SIZE,
      maxFiles: env.LOG_FILE_MAX_FILES,
      zippedArchive: true,
    })
  ],
  rejectionHandlers: [
    new DailyRotateFile({
      filename: path.join(logsDir, 'rejections-%DATE%.log'),
      datePattern: 'YYYY-MM-DD',
      format: logFormat,
      maxSize: env.LOG_FILE_MAX_SIZE,
      maxFiles: env.LOG_FILE_MAX_FILES,
      zippedArchive: true,
    })
  ]
});

// ============================================================================
// WINSTON ERROR HANDLER INTEGRATION
// ============================================================================

// Export Winston error handler methods for easy use across the application
export const logError = (error: any, context?: any, req?: Request) => {
  if (req) {
    winstonErrorHandler.logDeepError(error, error?.message || 'Error occurred', req, context);
  } else {
    winstonErrorHandler.logError(error, context);
  }
};

export const logWarning = (message: string, context?: any, req?: Request) => {
  winstonErrorHandler.logWarning(message, context, req);
};

export const logInfo = (message: string, context?: any, req?: Request) => {
  winstonErrorHandler.logInfo(message, context, req);
};

export const logHttp = (message: string, context?: any, req?: Request) => {
  winstonErrorHandler.logHttp(message, context, req);
};

export const logDebug = (message: string, context?: any) => {
  winstonErrorHandler.logDebug(message, context);
};

// Main error logging function - uses the new Winston error handler
export const logDeepError = (error: any, context: string, req?: Request, additionalData?: any) => {
  winstonErrorHandler.logDeepError(error, context, req, additionalData);
};

// Export the Winston error handler instance for advanced usage
export const getWinstonErrorHandler = () => winstonErrorHandler;

// Export error classification methods
export const isOperationalError = (error: any): boolean => {
  return winstonErrorHandler.isOperationalError(error);
};

export const getErrorSeverity = (error: any): 'low' | 'medium' | 'high' | 'critical' => {
  return winstonErrorHandler.getErrorSeverity(error);
};

export default logger;
