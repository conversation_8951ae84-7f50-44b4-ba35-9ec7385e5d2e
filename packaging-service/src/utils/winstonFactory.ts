import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from './winstonErrorHandler';
import path from 'path';

/**
 * <PERSON>
 * 
 * Creates pre-configured Winston error handlers for different microservices
 * Easy to use across multiple services with consistent configuration
 */

// Default configuration for microservices
const DEFAULT_CONFIG = {
  logLevel: process.env.LOG_LEVEL || 'info',
  logDir: path.join(process.cwd(), 'logs'),
  maxFileSize: process.env.LOG_FILE_MAX_SIZE || '20m',
  maxFiles: process.env.LOG_FILE_MAX_FILES || '14d',
  environment: process.env.NODE_ENV || 'development'
};

/**
 * Create <PERSON>r <PERSON> for Packaging Microservice
 */
export function createPackagingLogger(): WinstonErrorHandler {
  return new WinstonErrorHandler({
    serviceName: 'packaging-microservice',
    environment: DEFAULT_CONFIG.environment,
    logLevel: DEFAULT_CONFIG.logLevel,
    logDir: DEFAULT_CONFIG.logDir,
    maxFileSize: DEFAULT_CONFIG.maxFileSize,
    maxFiles: DEFAULT_CONFIG.maxFiles
  });
}

/**
 * Create <PERSON> Error Handler for User Microservice
 */
export function createUserLogger(): WinstonErrorHandler {
  return new WinstonErrorHandler({
    serviceName: 'user-microservice',
    environment: DEFAULT_CONFIG.environment,
    logLevel: DEFAULT_CONFIG.logLevel,
    logDir: DEFAULT_CONFIG.logDir,
    maxFileSize: DEFAULT_CONFIG.maxFileSize,
    maxFiles: DEFAULT_CONFIG.maxFiles
  });
}

/**
 * Create Winston Error Handler for Order Microservice
 */
export function createOrderLogger(): WinstonErrorHandler {
  return new WinstonErrorHandler({
    serviceName: 'order-microservice',
    environment: DEFAULT_CONFIG.environment,
    logLevel: DEFAULT_CONFIG.logLevel,
    logDir: DEFAULT_CONFIG.logDir,
    maxFileSize: DEFAULT_CONFIG.maxFileSize,
    maxFiles: DEFAULT_CONFIG.maxFiles
  });
}

/**
 * Create Winston Error Handler for API Gateway
 */
export function createApiGatewayLogger(): WinstonErrorHandler {
  return new WinstonErrorHandler({
    serviceName: 'api-gateway',
    environment: DEFAULT_CONFIG.environment,
    logLevel: DEFAULT_CONFIG.logLevel,
    logDir: DEFAULT_CONFIG.logDir,
    maxFileSize: DEFAULT_CONFIG.maxFileSize,
    maxFiles: DEFAULT_CONFIG.maxFiles
  });
}

/**
 * Create Winston Error Handler for any custom microservice
 */
export function createCustomLogger(serviceName: string, customConfig?: Partial<{
  logLevel: string;
  logDir: string;
  maxFileSize: string;
  maxFiles: string;
  environment: string;
}>): WinstonErrorHandler {
  return new WinstonErrorHandler({
    serviceName,
    environment: customConfig?.environment || DEFAULT_CONFIG.environment,
    logLevel: customConfig?.logLevel || DEFAULT_CONFIG.logLevel,
    logDir: customConfig?.logDir || DEFAULT_CONFIG.logDir,
    maxFileSize: customConfig?.maxFileSize || DEFAULT_CONFIG.maxFileSize,
    maxFiles: customConfig?.maxFiles || DEFAULT_CONFIG.maxFiles
  });
}

/**
 * Get default configuration for reference
 */
export function getDefaultConfig() {
  return { ...DEFAULT_CONFIG };
}

// Export the main class for direct use
export { WinstonErrorHandler };

/**
 * Usage Examples:
 * 
 * // For packaging microservice
 * const logger = createPackagingLogger();
 * logger.logDeepError(error, 'Package creation failed', req, { packageId: '123' });
 * 
 * // For custom microservice
 * const customLogger = createCustomLogger('payment-microservice');
 * customLogger.logWarning('Payment validation failed', { amount: 100 }, req);
 * 
 * // For API Gateway
 * const gatewayLogger = createApiGatewayLogger();
 * gatewayLogger.logInfo('Request routed successfully', { route: '/api/v1/packages' });
 */
