import { z } from "zod";

const envSchema = z.object({
  NODE_ENV: z.enum(["development", "production", "test"]).default("development"),
  PORT: z.string().transform(Number).default("3006"),
  MONGO_URI: z.string().min(1, "MongoDB URI is required"),
  JWT_SECRET: z.string().min(1, "JWT secret is required"),
  JWT_EXPIRE: z.string().default("7d"),
  ADMIN_JWT_SECRET: z.string().min(1, "Admin JWT secret is required"),
  ADMIN_JWT_EXPIRE: z.string().default("1d"),
  LOG_LEVEL: z.enum(["error", "warn", "info", "http", "verbose", "debug", "silly"]).default("info"),
  LOG_FILE_MAX_SIZE: z.string().default("20m"),
  LOG_FILE_MAX_FILES: z.string().default("14d"),
});

export const env = envSchema.parse(process.env);
