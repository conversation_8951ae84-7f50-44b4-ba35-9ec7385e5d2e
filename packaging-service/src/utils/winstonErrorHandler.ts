import winston from 'winston';
import DailyRotateFile from 'winston-daily-rotate-file';
import path from 'path';
import { Request } from 'express';

// Environment configuration interface
interface LoggerConfig {
  serviceName: string;
  environment: string;
  logLevel: string;
  logDir: string;
  maxFileSize: string;
  maxFiles: string;
}

// Error context interface
interface ErrorContext {
  [key: string]: any;
}

// Request context interface
interface RequestContext {
  method?: string;
  url?: string;
  originalUrl?: string;
  ip?: string;
  userAgent?: string;
  headers?: any;
  body?: any;
  params?: any;
  query?: any;
  user?: any;
}

/**
 * Winston Error Handler - Reusable across microservices
 * 
 * Features:
 * - Structured plain text logging focused on errors
 * - Daily log rotation with compression
 * - Request context tracking
 * - Error classification (operational vs programming)
 * - Deep error serialization with circular reference handling
 * - Environment-based configuration
 * - Easy integration with Express.js applications
 */
export class WinstonErrorHandler {
  private logger: winston.Logger;
  private config: LoggerConfig;

  constructor(config: LoggerConfig) {
    this.config = config;
    this.logger = this.createLogger();
  }

  /**
   * Create Winston logger with custom formatters and transports
   */
  private createLogger(): winston.Logger {
    // Ensure logs directory exists
    const fs = require('fs');
    if (!fs.existsSync(this.config.logDir)) {
      fs.mkdirSync(this.config.logDir, { recursive: true });
    }

    return winston.createLogger({
      level: this.config.logLevel,
      format: this.createLogFormat(),
      transports: this.createTransports(),
      exitOnError: false,
      exceptionHandlers: this.createExceptionHandlers(),
      rejectionHandlers: this.createRejectionHandlers()
    });
  }

  /**
   * Create custom log format - Clean, readable plain text
   */
  private createLogFormat() {
    const errorStackFormat = winston.format((info) => {
      if (info.error && info.error instanceof Error) {
        info.stack = info.error.stack;
        info.errorName = info.error.name;
        info.errorMessage = info.error.message;
        
        // Handle nested errors and circular references
        try {
          info.errorDetails = JSON.stringify(info.error, Object.getOwnPropertyNames(info.error), 2);
        } catch (e) {
          info.errorDetails = '[Circular Reference or Non-serializable]';
        }
        
        // Extract additional error properties
        const errorProps: any = {};
        Object.getOwnPropertyNames(info.error).forEach(key => {
          if (key !== 'name' && key !== 'message' && key !== 'stack') {
            try {
              errorProps[key] = (info.error as any)[key];
            } catch (e) {
              errorProps[key] = '[Circular Reference or Non-serializable]';
            }
          }
        });
        
        if (Object.keys(errorProps).length > 0) {
          info.errorProperties = errorProps;
        }
      }
      return info;
    });

    const requestContextFormat = winston.format((info) => {
      if (info.request) {
        const req = info.request as RequestContext;
        info.requestContext = {
          method: req.method || 'Unknown',
          url: req.url || req.originalUrl || 'Unknown',
          ip: req.ip || 'Unknown',
          userAgent: req.userAgent || req.headers?.['user-agent'] || 'Unknown',
          body: req.body && Object.keys(req.body).length > 0 ? req.body : null,
          params: req.params && Object.keys(req.params).length > 0 ? req.params : null,
          query: req.query && Object.keys(req.query).length > 0 ? req.query : null,
          user: req.user || null
        };
      }
      return info;
    });

    const readableFormatter = winston.format.printf(({ timestamp, level, message, ...meta }) => {
      let logOutput = `\n[${timestamp}] ${level.toUpperCase()}: ${message}\n`;
      
      // Error details - main focus
      if (meta.error || meta.errorName || meta.stack) {
        logOutput += `┌─ ERROR DETAILS\n`;
        logOutput += `│  Service: ${this.config.serviceName}\n`;
        logOutput += `│  Name: ${(meta as any).errorName || (meta as any).error?.name || 'Unknown'}\n`;
        logOutput += `│  Message: ${(meta as any).errorMessage || (meta as any).error?.message || message}\n`;
        
        if ((meta as any).stack || (meta as any).error?.stack) {
          logOutput += `│\n│  STACK TRACE:\n`;
          const stack = (meta as any).stack || (meta as any).error?.stack || '';
          const stackLines = stack.split('\n');
          stackLines.forEach((line: string, index: number) => {
            if (line.trim()) {
              if (index === 0) {
                logOutput += `│    → ${line}\n`;
              } else {
                logOutput += `│      ${line}\n`;
              }
            }
          });
        }
        
        // Additional error properties
        if ((meta as any).errorProperties && Object.keys((meta as any).errorProperties).length > 0) {
          logOutput += `│\n│  ERROR PROPERTIES:\n`;
          Object.entries((meta as any).errorProperties).forEach(([key, value]) => {
            logOutput += `│    ${key}: ${JSON.stringify(value)}\n`;
          });
        }
        logOutput += `└─\n`;
      }
      
      // Request information
      if ((meta as any).requestContext) {
        const req = (meta as any).requestContext;
        logOutput += `┌─ REQUEST INFO\n`;
        logOutput += `│  ${req.method} ${req.url}\n`;
        logOutput += `│  IP: ${req.ip}\n`;
        logOutput += `│  User-Agent: ${req.userAgent}\n`;
        
        if (req.body) {
          logOutput += `│  Body: ${JSON.stringify(req.body)}\n`;
        }
        
        if (req.params) {
          logOutput += `│  Params: ${JSON.stringify(req.params)}\n`;
        }
        
        if (req.query) {
          logOutput += `│  Query: ${JSON.stringify(req.query)}\n`;
        }
        
        if (req.user) {
          logOutput += `│  User: ${JSON.stringify({ id: req.user.id, email: req.user.email })}\n`;
        }
        logOutput += `└─\n`;
      }
      
      // Context information
      if ((meta as any).context) {
        logOutput += `┌─ CONTEXT\n`;
        if (typeof (meta as any).context === 'object') {
          Object.entries((meta as any).context).forEach(([key, value]) => {
            logOutput += `│  ${key}: ${JSON.stringify(value)}\n`;
          });
        } else {
          logOutput += `│  ${(meta as any).context}\n`;
        }
        logOutput += `└─\n`;
      }
      
      return logOutput;
    });

    return winston.format.combine(
      winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
      errorStackFormat(),
      requestContextFormat(),
      winston.format.errors({ stack: true }),
      readableFormatter
    );
  }

  /**
   * Create transport configurations
   */
  private createTransports(): winston.transport[] {
    const transports: winston.transport[] = [];

    // Console transport for development
    if (this.config.environment === 'development') {
      transports.push(
        new winston.transports.Console({
          format: winston.format.combine(
            winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
            winston.format.colorize(),
            winston.format.printf(({ timestamp, level, message, stack, errorName, requestContext }) => {
              let logMessage = `${timestamp} [${level}]: ${message}`;

              if (errorName) {
                logMessage += `\n  Error: ${errorName}`;
              }

              if (requestContext && typeof requestContext === 'object') {
                const ctx = requestContext as any;
                if (ctx.method && ctx.url) {
                  logMessage += `\n  Request: ${ctx.method} ${ctx.url}`;
                }
                if (ctx.ip) {
                  logMessage += `\n  IP: ${ctx.ip}`;
                }
              }

              if (stack) {
                logMessage += `\n  Stack: ${stack}`;
              }

              return logMessage;
            })
          ),
          level: this.config.logLevel,
        })
      );
    } else {
      // Simplified console for production
      transports.push(
        new winston.transports.Console({
          format: winston.format.combine(
            winston.format.timestamp(),
            winston.format.json()
          ),
          level: 'error',
        })
      );
    }

    // File transports with rotation
    transports.push(
      // Error logs (main focus)
      new DailyRotateFile({
        filename: path.join(this.config.logDir, 'error-%DATE%.log'),
        datePattern: 'YYYY-MM-DD',
        level: 'error',
        format: this.createLogFormat(),
        maxSize: this.config.maxFileSize,
        maxFiles: this.config.maxFiles,
        zippedArchive: true,
      }),

      // Combined logs
      new DailyRotateFile({
        filename: path.join(this.config.logDir, 'combined-%DATE%.log'),
        datePattern: 'YYYY-MM-DD',
        format: this.createLogFormat(),
        maxSize: this.config.maxFileSize,
        maxFiles: this.config.maxFiles,
        zippedArchive: true,
        level: this.config.logLevel,
      }),

      // HTTP access logs
      new DailyRotateFile({
        filename: path.join(this.config.logDir, 'access-%DATE%.log'),
        datePattern: 'YYYY-MM-DD',
        level: 'http',
        format: this.createLogFormat(),
        maxSize: this.config.maxFileSize,
        maxFiles: this.config.maxFiles,
        zippedArchive: true,
      })
    );

    return transports;
  }

  /**
   * Create exception handlers
   */
  private createExceptionHandlers(): winston.transport[] {
    return [
      new DailyRotateFile({
        filename: path.join(this.config.logDir, 'exceptions-%DATE%.log'),
        datePattern: 'YYYY-MM-DD',
        format: this.createLogFormat(),
        maxSize: this.config.maxFileSize,
        maxFiles: this.config.maxFiles,
        zippedArchive: true,
      })
    ];
  }

  /**
   * Create rejection handlers
   */
  private createRejectionHandlers(): winston.transport[] {
    return [
      new DailyRotateFile({
        filename: path.join(this.config.logDir, 'rejections-%DATE%.log'),
        datePattern: 'YYYY-MM-DD',
        format: this.createLogFormat(),
        maxSize: this.config.maxFileSize,
        maxFiles: this.config.maxFiles,
        zippedArchive: true,
      })
    ];
  }

  // ============================================================================
  // PUBLIC LOGGING METHODS
  // ============================================================================

  /**
   * Log deep error with full context - Main error logging method
   */
  public logDeepError(
    error: Error,
    message: string,
    request?: Request | RequestContext,
    context?: ErrorContext
  ): void {
    this.logger.error(message, {
      error,
      request,
      context: {
        ...context,
        timestamp: new Date().toISOString(),
        service: this.config.serviceName
      }
    });
  }

  /**
   * Log warning (operational errors)
   */
  public logWarning(
    message: string,
    context?: ErrorContext,
    request?: Request | RequestContext
  ): void {
    this.logger.warn(message, {
      request,
      context: {
        ...context,
        timestamp: new Date().toISOString(),
        service: this.config.serviceName
      }
    });
  }

  /**
   * Log error without full context
   */
  public logError(
    error: Error | string,
    context?: ErrorContext
  ): void {
    if (typeof error === 'string') {
      this.logger.error(error, {
        context: {
          ...context,
          timestamp: new Date().toISOString(),
          service: this.config.serviceName
        }
      });
    } else {
      this.logger.error(error.message, {
        error,
        context: {
          ...context,
          timestamp: new Date().toISOString(),
          service: this.config.serviceName
        }
      });
    }
  }

  /**
   * Log info
   */
  public logInfo(
    message: string,
    context?: ErrorContext,
    request?: Request | RequestContext
  ): void {
    this.logger.info(message, {
      request,
      context: {
        ...context,
        timestamp: new Date().toISOString(),
        service: this.config.serviceName
      }
    });
  }

  /**
   * Log HTTP requests
   */
  public logHttp(
    message: string,
    context?: ErrorContext,
    request?: Request | RequestContext
  ): void {
    this.logger.http(message, {
      request,
      context: {
        ...context,
        timestamp: new Date().toISOString(),
        service: this.config.serviceName
      }
    });
  }

  /**
   * Log debug information
   */
  public logDebug(
    message: string,
    context?: ErrorContext
  ): void {
    this.logger.debug(message, {
      context: {
        ...context,
        timestamp: new Date().toISOString(),
        service: this.config.serviceName
      }
    });
  }

  // ============================================================================
  // ERROR CLASSIFICATION METHODS
  // ============================================================================

  /**
   * Check if error is operational (expected) vs programming error
   */
  public isOperationalError(error: any): boolean {
    // Zod validation errors
    if (error.name === 'ZodError') return true;

    // Mongoose validation errors
    if (error.name === 'ValidationError') return true;
    if (error.name === 'CastError') return true;
    if (error.code === 11000) return true; // Duplicate key

    // JWT errors
    if (error.name === 'JsonWebTokenError') return true;
    if (error.name === 'TokenExpiredError') return true;
    if (error.name === 'NotBeforeError') return true;

    // HTTP client errors (4xx)
    if (error.status >= 400 && error.status < 500) return true;
    if (error.statusCode >= 400 && error.statusCode < 500) return true;

    // Custom operational errors
    if (error.isOperational) return true;

    return false;
  }

  /**
   * Get error severity level
   */
  public getErrorSeverity(error: any): 'low' | 'medium' | 'high' | 'critical' {
    // Critical: Database connection, server crashes
    if (error.code === 'ECONNREFUSED' || error.code === 'ETIMEDOUT') return 'critical';
    if (error.name === 'MongoNetworkError') return 'critical';

    // High: Authentication failures, data corruption
    if (error.name === 'JsonWebTokenError') return 'high';
    if (error.name === 'TokenExpiredError') return 'high';

    // Medium: Validation errors, not found errors
    if (error.name === 'ZodError') return 'medium';
    if (error.name === 'ValidationError') return 'medium';
    if (error.status === 404 || error.statusCode === 404) return 'medium';

    // Low: Client-side errors, rate limiting
    if (error.status >= 400 && error.status < 500) return 'low';
    if (error.statusCode >= 400 && error.statusCode < 500) return 'low';

    // Default to high for unknown errors
    return 'high';
  }

  // ============================================================================
  // UTILITY METHODS
  // ============================================================================

  /**
   * Get the underlying Winston logger instance
   */
  public getLogger(): winston.Logger {
    return this.logger;
  }

  /**
   * Create request context from Express request
   */
  public createRequestContext(req: Request): RequestContext {
    return {
      method: req.method,
      url: req.originalUrl,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      headers: req.headers,
      body: req.body,
      params: req.params,
      query: req.query,
      user: (req as any).user
    };
  }

  /**
   * Serialize error safely (handles circular references)
   */
  public serializeError(error: Error): any {
    const serialized: any = {};

    Object.getOwnPropertyNames(error).forEach(key => {
      try {
        serialized[key] = (error as any)[key];
      } catch (e) {
        serialized[key] = '[Circular Reference or Non-serializable]';
      }
    });

    return serialized;
  }
}
