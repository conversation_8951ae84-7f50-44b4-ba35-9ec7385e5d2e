import express from "express";
import { setupMiddleware } from "./config/middleware";
import { setupRoutes } from "./config/routes";
import { setupErrorHandling } from "./config/errorHandling";
import { connectDB } from "./config/db";

const app = express();

// Database connection
connectDB();

// Setup middleware
setupMiddleware(app);

// Setup routes
setupRoutes(app);

// Setup error handling
setupErrorHandling(app);

export default app;