import dotenv from "dotenv";
dotenv.config();
import app from "./app";
import { env } from "./utils/envValidator";
import { logInfo, logError } from "./utils/logger";

const PORT = env.PORT || 3006;

const server = app.listen(PORT, '0.0.0.0', () => {
  logInfo(`Server started successfully on port ${PORT}`, {
    port: PORT,
    environment: env.NODE_ENV,
    nodeVersion: process.version,
    timestamp: new Date().toISOString()
  });
});

// Graceful shutdown handling
const gracefulShutdown = (signal: string) => {
  logInfo(`${signal} received. Starting graceful shutdown...`);

  server.close((error) => {
    if (error) {
      logError(error, { context: 'Error during server shutdown' });
      process.exit(1);
    }

    logInfo('Server closed successfully');
    process.exit(0);
  });

  setTimeout(() => {
    logError(new Error('Forced shutdown after timeout'), {
      context: 'Graceful shutdown timeout'
    });
    process.exit(1);
  }, 10000);
};

// Handle shutdown signals
process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
process.on('SIGINT', () => gracefulShutdown('SIGINT'));

// Handle server errors
server.on('error', (error: any) => {
  if (error.code === 'EADDRINUSE') {
    logError(error, {
      context: `Port ${PORT} is already in use`,
      port: PORT
    });
  } else {
    logError(error, { context: 'Server error' });
  }
  process.exit(1);
});
