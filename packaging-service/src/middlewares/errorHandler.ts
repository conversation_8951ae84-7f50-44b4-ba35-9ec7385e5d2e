import { Request, Response, NextFunction } from "express";
import { ZodError } from "zod";
import { MongooseError } from "mongoose";
import { JsonWebTokenError, TokenExpiredError } from "jsonwebtoken";
import { logDeepError, logWarning, isOperationalError } from "../utils/logger";

interface CustomError extends Error {
  statusCode?: number;
  status?: number;
  code?: number;
  keyValue?: any;
  errors?: any;
  isOperational?: boolean;
}

// Note: isOperationalError is now imported from the Winston error handler

export const errorHandler = (
  err: CustomError,
  req: Request,
  res: Response,
  _next: NextFunction
): void => {
  let error = { ...err };
  error.message = err.message;

  // Determine error severity and log accordingly
  const isOperational = isOperationalError(err);
  const errorContext = {
    url: req.originalUrl,
    method: req.method,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    userId: (req as any).user?.id,
    timestamp: new Date().toISOString(),
    isOperational,
    errorCode: err.code,
    statusCode: err.statusCode || err.status,
  };

  if (isOperational) {
    // Log operational errors as warnings with context
    logWarning(`Operational error: ${err.message}`, errorContext, req);
  } else {
    // Log programming errors as errors with full details
    logDeepError(err, 'Unhandled application error', req, errorContext);
  }

  // Zod validation error
  if (err instanceof ZodError) {
    const message = "Validation Error";
    const errors = err.errors.map((e) => ({
      field: e.path.join("."),
      message: e.message,
    }));

    logWarning(`Zod validation error: ${message}`, {
      ...errorContext,
      validationErrors: errors,
      zodIssues: err.issues
    }, req);

    res.status(400).json({
      success: false,
      message,
      errors,
    });
    return;
  }

  // Mongoose bad ObjectId
  if (err.name === "CastError") {
    const message = "Resource not found";

    logWarning(`Mongoose CastError: ${message}`, {
      ...errorContext,
      castError: {
        value: (err as any).value,
        path: (err as any).path,
        kind: (err as any).kind
      }
    }, req);

    res.status(404).json({
      success: false,
      message,
    });
    return;
  }

  // Mongoose duplicate key
  if (err.code === 11000) {
    const message = "Duplicate field value entered";

    logWarning(`MongoDB duplicate key error: ${message}`, {
      ...errorContext,
      duplicateKey: err.keyValue,
      mongoError: {
        code: err.code,
        codeName: (err as any).codeName,
        index: (err as any).index
      }
    }, req);

    res.status(400).json({
      success: false,
      message,
    });
    return;
  }

  // Mongoose validation error
  if (err.name === "ValidationError") {
    const message = "Validation Error";
    const errors = Object.values(err.errors || {}).map((val: any) => ({
      field: val.path,
      message: val.message,
    }));

    logWarning(`Mongoose validation error: ${message}`, {
      ...errorContext,
      validationErrors: errors,
      mongooseErrors: err.errors
    }, req);

    res.status(400).json({
      success: false,
      message,
      errors,
    });
    return;
  }

  // JWT errors
  if (err instanceof JsonWebTokenError) {
    const message = "Invalid token";

    logWarning(`JWT error: ${message}`, {
      ...errorContext,
      jwtError: {
        name: err.name,
        message: err.message
      }
    }, req);

    res.status(401).json({
      success: false,
      message,
    });
    return;
  }

  if (err instanceof TokenExpiredError) {
    const message = "Token expired";

    logWarning(`JWT token expired: ${message}`, {
      ...errorContext,
      expiredAt: (err as any).expiredAt
    }, req);

    res.status(401).json({
      success: false,
      message,
    });
    return;
  }

  // Default error - this handles unexpected errors
  const status = error.statusCode || error.status || 500;
  const message = error.message || "Server Error";

  // Log unexpected errors with full details
  if (status >= 500) {
    logDeepError(err, `Unexpected server error: ${message}`, req, {
      ...errorContext,
      fullError: {
        ...err,
        errorName: err.name,
        errorMessage: err.message,
        errorStack: err.stack
      }
    });
  } else {
    logWarning(`Client error: ${message}`, errorContext, req);
  }

  res.status(status).json({
    success: false,
    message,
  });
};
