import { Request, Response, NextFunction } from "express";
import { logHttp, logWarning, logError, getWinstonError<PERSON>and<PERSON> } from "../utils/logger";

interface RequestWithStartTime extends Request {
  startTime?: number;
}

// HTTP request logging middleware using Winston
export const httpLogger = (req: RequestWithStartTime, res: Response, next: NextFunction) => {
  // Record start time
  req.startTime = Date.now();

  // Get original end function
  const originalEnd = res.end.bind(res);

  // Override res.end to capture response details
  res.end = function(this: Response, chunk?: any, encoding?: any, cb?: any): Response {
    // Calculate response time
    const responseTime = req.startTime ? Date.now() - req.startTime : 0;
    
    // Get response size
    const contentLength = res.get('content-length') || 
                         (chunk ? Buffer.byteLength(chunk, encoding) : 0);
    
    // Determine log level based on status code
    const statusCode = res.statusCode;
    const logLevel = statusCode >= 400 ? 'warn' : 'http';
    
    // Create log message
    const logMessage = `${req.method} ${req.originalUrl} ${statusCode} ${responseTime}ms - ${contentLength} bytes`;
    
    // Create detailed context
    const context = {
      request: {
        method: req.method,
        url: req.originalUrl,
        path: req.path,
        query: req.query,
        params: req.params,
        headers: {
          'user-agent': req.get('User-Agent'),
          'content-type': req.get('Content-Type'),
          'content-length': req.get('Content-Length'),
          'authorization': req.get('Authorization') ? '[REDACTED]' : undefined,
          'x-forwarded-for': req.get('X-Forwarded-For'),
          'x-real-ip': req.get('X-Real-IP'),
        },
        ip: req.ip || req.connection?.remoteAddress,
        protocol: req.protocol,
        secure: req.secure,
        xhr: req.xhr,
      },
      response: {
        statusCode,
        statusMessage: res.statusMessage,
        headers: {
          'content-type': res.get('Content-Type'),
          'content-length': contentLength,
          'cache-control': res.get('Cache-Control'),
          'etag': res.get('ETag'),
        },
        responseTime,
        size: contentLength,
      },
      performance: {
        responseTime,
        timestamp: new Date().toISOString(),
      }
    };
    
    // Log based on status code with appropriate severity
    if (statusCode >= 500) {
      // Server errors are more serious - use error level
      logError(new Error(`Server Error: ${logMessage}`), context, req);
    } else if (statusCode >= 400) {
      // Client errors are operational - use warning level
      logWarning(`Client Error: ${logMessage}`, context, req);
    } else if (responseTime > 1000) {
      // Slow requests should be highlighted
      logWarning(`Slow Request: ${logMessage}`, {
        ...context,
        slowRequestThreshold: 1000,
        performanceIssue: true
      }, req);
    } else {
      // Normal requests
      logHttp(logMessage, context, req);
    }
    
    // Call original end function and return the response
    return originalEnd(chunk, encoding, cb);
  };
  
  next();
};

// Middleware to log slow requests
export const slowRequestLogger = (threshold: number = 1000) => {
  return (req: RequestWithStartTime, res: Response, next: NextFunction) => {
    req.startTime = Date.now();

    const originalEnd = res.end.bind(res);

    res.end = function(this: Response, chunk?: any, encoding?: any, cb?: any): Response {
      const responseTime = req.startTime ? Date.now() - req.startTime : 0;
      
      if (responseTime > threshold) {
        logWarning(`Slow request detected: ${req.method} ${req.originalUrl}`, {
          responseTime,
          threshold,
          url: req.originalUrl,
          method: req.method,
          statusCode: res.statusCode,
          userAgent: req.get('User-Agent'),
          ip: req.ip,
        }, req);
      }
      
      return originalEnd(chunk, encoding, cb);
    };
    
    next();
  };
};

// Middleware to log request body for debugging (use carefully in production)
export const requestBodyLogger = (req: Request, res: Response, next: NextFunction) => {
  if (req.method !== 'GET' && req.method !== 'HEAD') {
    const context = {
      method: req.method,
      url: req.originalUrl,
      contentType: req.get('Content-Type'),
      contentLength: req.get('Content-Length'),
      body: req.body,
      timestamp: new Date().toISOString(),
    };
    
    logHttp(`Request body: ${req.method} ${req.originalUrl}`, context, req);
  }
  
  next();
};
