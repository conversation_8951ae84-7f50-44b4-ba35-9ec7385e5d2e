import { Request, Response, NextFunction } from "express";
import jwt from "jsonwebtoken";
import { env } from "../utils/envValidator";
import { logInfo, logError } from "../utils/logger";

interface AuthRequest extends Request {
  user?: any;
}

export const protect = async (
  req: AuthRequest,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    let token: string | undefined;

    if (
      req.headers.authorization &&
      req.headers.authorization.startsWith("Bearer")
    ) {
      token = req.headers.authorization.split(" ")[1];
    }

    if (!token) {
      res.status(401).json({
        success: false,
        message: "Not authorized to access this route",
      });
      return;
    }

    try {
      const decoded = jwt.verify(token, env.JWT_SECRET) as any;
      req.user = decoded;
      next();
    } catch (error) {
      res.status(401).json({
        success: false,
        message: "Not authorized to access this route",
      });
      return;
    }
  } catch (error) {
    next(error);
  }
};

export const adminProtect = async (
  req: AuthRequest,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    let token: string | undefined;

    if (
      req.headers.authorization &&
      req.headers.authorization.startsWith("Bearer")
    ) {
      token = req.headers.authorization.split(" ")[1];
    }

    if (!token) {
      logInfo("Admin access denied: No token provided", {
        ip: req.ip,
        userAgent: req.get('User-Agent'),
        path: req.path
      });
      res.status(401).json({
        success: false,
        status: 401,
        message: "Not authorized to access this route",
      });
      return;
    }

    try {
      // First try to verify with ADMIN_JWT_SECRET
      const decoded = jwt.verify(token, env.ADMIN_JWT_SECRET) as any;
      logInfo("Admin access granted with ADMIN_JWT_SECRET", {
        userId: decoded.userId || decoded.id,
        role: decoded.role,
        path: req.path
      });
      req.user = decoded;
      next();
    } catch (adminError) {
      try {
        // If admin secret fails, try regular JWT_SECRET and check for admin role
        const decoded = jwt.verify(token, env.JWT_SECRET) as any;

        // Check if user has admin role
        if (decoded.role === 'admin' || decoded.isAdmin === true || decoded.admin === true) {
          logInfo("Admin access granted with JWT_SECRET", {
            userId: decoded.userId || decoded.id,
            role: decoded.role,
            isAdmin: decoded.isAdmin,
            admin: decoded.admin,
            path: req.path
          });
          req.user = decoded;
          next();
        } else {
          logInfo("Admin access denied: Insufficient permissions", {
            userId: decoded.userId || decoded.id,
            role: decoded.role,
            isAdmin: decoded.isAdmin,
            admin: decoded.admin,
            path: req.path
          });
          res.status(403).json({
            success: false,
            status: 403,
            message: "Admin access required",
          });
          return;
        }
      } catch (regularError) {
        logError(regularError as Error, {
          context: "Admin authentication failed",
          path: req.path,
          hasToken: !!token
        });
        res.status(401).json({
          success: false,
          status: 401,
          message: "Invalid token or insufficient permissions",
        });
        return;
      }
    }
  } catch (error) {
    next(error);
  }
};
