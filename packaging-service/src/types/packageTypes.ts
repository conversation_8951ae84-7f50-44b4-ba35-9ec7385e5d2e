import { PackageDocument } from "../models/Package";

// Create Package DTO
export interface CreatePackageDto {
  title: string;
  shortDescription: string;
  description: string;
  categories?: {
    customPrinting?: string[];
    packagingProducts?: string[];
    bundlesSamples?: string[];
    shopByCollection?: string[];
  };
  variations: {
    title: string;
    optionType: "images" | "default" | "dropdown" | "checkbox" | "radio";
    options: {
      text?: string;
      url?: string;
      image?: {
        id: string;
        file: any | null;
        preview: string | null;
      };
    }[];
  }[];
  images: string[];
  quantity: {
    size: string;
    price: number;
  }[];
}

// Update Package DTO
export interface UpdatePackageDto {
  title?: string;
  shortDescription?: string;
  description?: string;
  categories?: Partial<PackageDocument["categories"]>;
  variations?: Partial<PackageDocument["variations"]>;
  images?: string[];
  quantity?: Partial<PackageDocument["quantity"]>;
}

// Query Package DTO
export interface QueryPackageDto {
  page?: number;
  limit?: number;
  search?: string;
  category?: string;
  sortBy?: "createdAt" | "title" | "price";
  sortOrder?: "asc" | "desc";
}

// Package Response DTO
export interface PackageResponseDto {
  success: boolean;
  message: string;
  data?: PackageDocument | PackageDocument[];
  count?: number;
  pagination?: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
}
