# Environment Configuration
NODE_ENV=production
PORT=3007

# Database Configuration
MONGO_URI=mongodb+srv://username:<EMAIL>/database?retryWrites=true&w=majority&appName=YourApp

# JWT Configuration
JWT_SECRET=your-jwt-secret-key-here
JWT_EXPIRE=7d
ADMIN_JWT_SECRET=your-admin-jwt-secret-key-here
ADMIN_JWT_EXPIRE=1d

# Winston Logging Configuration
LOG_LEVEL=info
LOG_FILE_MAX_SIZE=20m
LOG_FILE_MAX_FILES=14d

# CORS Configuration
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:3001,http://localhost:5173,http://localhost:8080
CORS_CREDENTIALS=true
CORS_MAX_AGE=86400