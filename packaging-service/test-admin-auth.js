#!/usr/bin/env node

const jwt = require('jsonwebtoken');

// Test JWT generation and verification
const JWT_SECRET = 'coconut-secret';
const ADMIN_JWT_SECRET = 'coconut-secret-admin';

console.log('🧪 Testing Admin Authentication...\n');

// Test 1: Generate regular JWT with admin role
const regularAdminPayload = {
  userId: 'admin123',
  email: '<EMAIL>',
  role: 'admin',
  isAdmin: true,
  iat: Math.floor(Date.now() / 1000),
  exp: Math.floor(Date.now() / 1000) + (7 * 24 * 60 * 60) // 7 days
};

const regularAdminToken = jwt.sign(regularAdminPayload, JWT_SECRET);
console.log('✅ Regular JWT with admin role:');
console.log(`Token: ${regularAdminToken.substring(0, 50)}...`);
console.log(`Payload:`, regularAdminPayload);
console.log();

// Test 2: Generate dedicated admin JWT
const adminPayload = {
  userId: 'admin123',
  email: '<EMAIL>',
  role: 'admin',
  iat: Math.floor(Date.now() / 1000),
  exp: Math.floor(Date.now() / 1000) + (1 * 24 * 60 * 60) // 1 day
};

const adminToken = jwt.sign(adminPayload, ADMIN_JWT_SECRET);
console.log('✅ Dedicated Admin JWT:');
console.log(`Token: ${adminToken.substring(0, 50)}...`);
console.log(`Payload:`, adminPayload);
console.log();

// Test 3: Verify tokens
console.log('🔍 Token Verification Tests:');

try {
  const decoded1 = jwt.verify(regularAdminToken, JWT_SECRET);
  console.log('✅ Regular JWT verified with JWT_SECRET');
  console.log('   Admin check:', decoded1.role === 'admin' || decoded1.isAdmin === true);
} catch (error) {
  console.log('❌ Regular JWT verification failed:', error.message);
}

try {
  const decoded2 = jwt.verify(adminToken, ADMIN_JWT_SECRET);
  console.log('✅ Admin JWT verified with ADMIN_JWT_SECRET');
} catch (error) {
  console.log('❌ Admin JWT verification failed:', error.message);
}

console.log('\n📋 Test Commands for API:');
console.log('\n1. Using Regular JWT with admin role:');
console.log(`curl -H "Authorization: Bearer ${regularAdminToken}" \\`);
console.log(`     -H "Content-Type: application/json" \\`);
console.log(`     -d '{"title":"Test Package","shortDescription":"Admin test"}' \\`);
console.log(`     https://coconut-packaging-api.onrender.com/api/v1/packages`);

console.log('\n2. Using Dedicated Admin JWT:');
console.log(`curl -H "Authorization: Bearer ${adminToken}" \\`);
console.log(`     -H "Content-Type: application/json" \\`);
console.log(`     -d '{"title":"Test Package","shortDescription":"Admin test"}' \\`);
console.log(`     https://coconut-packaging-api.onrender.com/api/v1/packages`);

console.log('\n🎯 Copy one of the above curl commands to test the API!');
