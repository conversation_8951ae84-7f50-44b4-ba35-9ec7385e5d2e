#!/bin/bash

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE} Starting Coconut Packaging API with ngrok...${NC}"

# Kill any existing processes on port 3007
echo -e "${YELLOW}🔍 Checking for existing processes on port 3007...${NC}"
lsof -ti:3007 | xargs kill -9 2>/dev/null || true

# Kill any existing ngrok processes
echo -e "${YELLOW} Stopping existing ngrok processes...${NC}"
pkill -f ngrok 2>/dev/null || true

# Wait a moment for processes to stop
sleep 2

# Start the server in background
echo -e "${YELLOW} Starting server on port 3007...${NC}"
npm start &
SERVER_PID=$!

# Wait for server to start
echo -e "${YELLOW}⏳ Waiting for server to start...${NC}"
sleep 5

# Check if server is running
if ! lsof -ti:3007 > /dev/null; then
    echo -e "${RED} Server failed to start on port 3007${NC}"
    exit 1
fi

echo -e "${GREEN} Server started successfully on port 3007${NC}"

# Start ngrok tunnel
echo -e "${YELLOW}🌐 Starting ngrok tunnel...${NC}"
ngrok http 3007 --log=stdout &
NGROK_PID=$!

# Wait for ngrok to start
sleep 3

# Get ngrok URL
echo -e "${YELLOW}🔍 Getting ngrok URL...${NC}"
NGROK_URL=$(curl -s http://localhost:4040/api/tunnels | jq -r '.tunnels[0].public_url' 2>/dev/null)

if [ "$NGROK_URL" != "null" ] && [ -n "$NGROK_URL" ]; then
    echo -e "${GREEN} ngrok tunnel established!${NC}"
    echo -e "${BLUE}📱 Public URL: ${NGROK_URL}${NC}"
    echo -e "${BLUE} API Base: ${NGROK_URL}/api/v1${NC}"
    echo -e "${BLUE} API Docs: ${NGROK_URL}/api-docs${NC}"
    echo -e "${BLUE} Local URL: http://localhost:3007${NC}"
    echo ""
    echo -e "${YELLOW}🧪 Test endpoints:${NC}"
    echo -e "  curl ${NGROK_URL}/"
    echo -e "  curl ${NGROK_URL}/api/v1/packages"
    echo ""
    echo -e "${YELLOW} Process IDs:${NC}"
    echo -e "  Server PID: ${SERVER_PID}"
    echo -e "  ngrok PID: ${NGROK_PID}"
    echo ""
    echo -e "${GREEN} Setup complete! Press Ctrl+C to stop both services.${NC}"
else
    echo -e "${RED} Failed to get ngrok URL${NC}"
    echo -e "${YELLOW}🔍 Checking ngrok status...${NC}"
    curl -s http://localhost:4040/api/tunnels | jq . 2>/dev/null || echo "ngrok API not responding"
fi

# Function to cleanup on exit
cleanup() {
    echo -e "\n${YELLOW} Shutting down...${NC}"
    kill $NGROK_PID 2>/dev/null || true
    kill $SERVER_PID 2>/dev/null || true
    pkill -f ngrok 2>/dev/null || true
    lsof -ti:3007 | xargs kill -9 2>/dev/null || true
    echo -e "${GREEN} Cleanup complete${NC}"
    exit 0
}

# Set trap to cleanup on script exit
trap cleanup SIGINT SIGTERM

# Keep script running
while true; do
    sleep 1
done
