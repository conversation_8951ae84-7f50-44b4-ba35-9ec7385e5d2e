{"compilerOptions": {"target": "ES2020", "module": "commonjs", "lib": ["ES2020"], "outDir": "./dist", "rootDir": "./src", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "declaration": false, "removeComments": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "sourceMap": false, "incremental": true, "tsBuildInfoFile": "./dist/.tsbuildinfo"}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "**/*.test.ts"]}