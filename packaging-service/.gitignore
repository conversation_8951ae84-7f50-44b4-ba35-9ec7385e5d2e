# Dependency directories
node_modules/

# Logs (DO NOT UPLOAD)
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Environment variables (CRITICAL - DO NOT UPLOAD)
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Build outputs
dist/
build/

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Test coverage
coverage/

# Editor/IDE settings
.vscode/
.idea/
*.swp
*.swo

# OS-specific files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Temporary files
*.tmp
*.temp

# TypeScript
*.tsbuildinfo